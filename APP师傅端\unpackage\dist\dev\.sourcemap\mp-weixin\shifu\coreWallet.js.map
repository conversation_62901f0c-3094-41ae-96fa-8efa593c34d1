{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/coreWallet.vue?8a88", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/coreWallet.vue?ace5", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/coreWallet.vue?4481", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/coreWallet.vue?6cc7", "uni-app:///shifu/coreWallet.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/coreWallet.vue?3fb9", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/coreWallet.vue?a5fe"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "totalAmount", "currentStatus", "recordList", "loading", "hasMore", "pageNum", "pageSize", "statusOptions", "label", "value", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "initData", "refreshData", "uni", "getTotalAmount", "res", "console", "getRecordList", "reset", "params", "status", "title", "icon", "loadMore", "changeStatus", "handleRecordTap", "handleStatusTap", "item", "viewDetail", "url", "processWithdrawal", "content", "confirmText", "cancelText", "success", "processCancel", "<PERSON><PERSON><PERSON><PERSON>wal", "id", "mchId", "appId", "package", "setTimeout", "fail", "complete", "showCancel", "executeCancel", "getCashToTypeText", "getStatusClass"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gUAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvDA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+D72B;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;IAEA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBACAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAGAC;kBACAC;kBACA;kBACAlB;kBACAC;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAY;gBAEA;kBACAnB;kBACA;oBACA;oBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;kBACA;oBACA;kBACA;gBACA;kBACAiB;oBACAQ;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAN;gBACAH;kBACAQ;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACAf;QACAgB;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAjB;kBACAQ;kBACAU;kBACAC;kBACAC;kBACAC;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAnB;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,OACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAoB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAtB;kBACAQ;kBACAU;kBACAC;kBACAC;kBACAC;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAnB;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,OACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAqB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;kBACA;;kBAEA;gBAAA;gBA0BAvB;kBAAAQ;gBAAA;gBAAA;gBAAA;gBAAA,OAGA;kBAAAgB;gBAAA;cAAA;gBAAAtB;gBAEA;kBACA;kBACA;oBACAC;oBACA;oBACA;sBACA;sBACAH;wBACAyB;wBACAC;wBACAC;wBACAN;0BACArB;4BACAQ;4BACAC;0BACA;0BACAmB;4BACA;0BACA;wBACA;wBACAC;0BACA7B;4BACAQ;4BACAC;0BACA;wBACA;wBACAqB;0BACA9B;wBACA;sBACA;oBACA;sBACAA;sBACAA;wBACAQ;wBACAU;wBACAa;sBACA;oBACA;kBACA;oBACA;oBACA/B;sBACAQ;sBACAC;oBACA;oBACAmB;sBACA;oBACA;kBACA;gBACA;kBACA5B;oBACAQ;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAN;gBACAH;kBACAQ;kBACAC;gBACA;cAAA;gBAAA;gBAEAT;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAhC;kBAAAQ;gBAAA;gBAAA;gBAEAF;kBACAkB;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAtB;gBAAA;gBACA;kBACAF;oBACAQ;oBACAC;kBACA;kBACA;gBACA;kBACAT;oBACAQ;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAN;gBACAH;kBACAQ;kBACAC;gBACA;cAAA;gBAAA;gBAEAT;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QAAA;QACA;QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtZA;AAAA;AAAA;AAAA;AAAgmD,CAAgB,ojDAAG,EAAC,C;;;;;;;;;;;ACApnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/coreWallet.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/coreWallet.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./coreWallet.vue?vue&type=template&id=f9db71dc&scoped=true&\"\nvar renderjs\nimport script from \"./coreWallet.vue?vue&type=script&lang=js&\"\nexport * from \"./coreWallet.vue?vue&type=script&lang=js&\"\nimport style0 from \"./coreWallet.vue?vue&type=style&index=0&id=f9db71dc&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f9db71dc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/coreWallet.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coreWallet.vue?vue&type=template&id=f9db71dc&scoped=true&\"", "var components\ntry {\n  components = {\n    uScrollList: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-scroll-list/u-scroll-list\" */ \"uview-ui/components/u-scroll-list/u-scroll-list.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.recordList, function (item, __i1__) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.getStatusClass(item)\n    var m1 = _vm.getCashToTypeText(item.cashToType)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  var g0 = _vm.recordList.length === 0 && !_vm.loading\n  var g1 = _vm.recordList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coreWallet.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coreWallet.vue?vue&type=script&lang=js&\"", "\r\n<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"header-stats\">\r\n\t\t\t<view class=\"stats-card\">\r\n\t\t\t\t<view class=\"stats-title\">已到账总额</view>\r\n\t\t\t\t<view class=\"stats-amount\">￥{{ totalAmount }}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"filter-section\">\r\n\t\t\t<u-scroll-list :indicator=\"true\" indicator-color=\"#f2f2f2\" indicator-active-color=\"#2e80fe\">\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"filter-tab\"\r\n\t\t\t\t\t:class=\"{ active: currentStatus === item.value }\"\r\n\t\t\t\t\tv-for=\"item in statusOptions\"\r\n\t\t\t\t\t:key=\"item.value\"\r\n\t\t\t\t\t@tap=\"changeStatus(item.value)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{{ item.label }}\r\n\t\t\t\t</view>\r\n\t\t\t</u-scroll-list>\r\n\t\t</view>\r\n\t\t<view class=\"record-list\">\r\n\t\t\t<view\r\n\t\t\t\tclass=\"record-item\"\r\n\t\t\t\tv-for=\"item in recordList\"\r\n\t\t\t\t:key=\"item.id\"\r\n\t\t\t\t@tap=\"handleRecordTap(item)\"\r\n\t\t\t>\r\n\t\t\t\t<view class=\"record-header\">\r\n\t\t\t\t\t<view class=\"record-amount\">￥{{ item.amount }}</view>\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tclass=\"record-status\"\r\n\t\t\t\t\t\t:class=\"[getStatusClass(item)]\"\r\n\t\t\t\t\t\**********=\"handleStatusTap(item)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{{ item.statusText }}\r\n\t\t\t\t\t\t<text v-if=\"item.lock === 1\" class=\"tap-hint\">点击提现</text>\r\n\t\t\t\t\t\t<text v-if=\"item.lock === 0\" class=\"tap-hint\">点击取消</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"record-info\">\r\n\t\t\t\t\t<view class=\"record-time\">{{ item.createTime }}</view>\r\n\t\t\t\t\t<view class=\"record-method\">提现到：{{ getCashToTypeText(item.cashToType) }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"empty-state\" v-if=\"recordList.length === 0 && !loading\">\r\n\t\t\t<image src=\"/static/mine/default_user.png\" class=\"empty-image\"></image>\r\n\t\t\t<text class=\"empty-text\">暂无提现记录</text>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"load-more\" v-if=\"recordList.length > 0\">\r\n\t\t\t<text class=\"load-text\" v-if=\"hasMore && !loading\">上拉加载更多</text>\r\n\t\t\t<text class=\"load-text\" v-if=\"loading\">加载中...</text>\r\n\t\t\t<text class=\"load-text\" v-if=\"!hasMore && !loading\">没有更多数据了</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttotalAmount: 0, // 已到账总额\r\n\t\t\t\tcurrentStatus: '', // 当前选中的状态\r\n\t\t\t\trecordList: [], // 提现记录列表\r\n\t\t\t\tloading: false, // 加载状态\r\n\t\t\t\thasMore: true, // 是否还有更多数据\r\n\t\t\t\tpageNum: 1, // 当前页码\r\n\t\t\t\tpageSize: 10, // 每页数量\r\n\t\t\t\tstatusOptions: [\r\n\t\t\t\t\t{ label: '全部', value: '' },\r\n\t\t\t\t\t{ label: '未提现', value: 0 },\r\n\t\t\t\t\t{ label: '已提现未领取', value: 1 },\r\n\t\t\t\t\t{ label: '已到账', value: 2 },\r\n\t\t\t\t\t{ label: '失败', value: 3 },\r\n\t\t\t\t\t{ label: '关闭', value: 4 }\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.initData();\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\tthis.refreshData();\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tthis.loadMore();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 初始化数据\r\n\t\t\tasync initData() {\r\n\t\t\t\tawait this.getTotalAmount();\r\n\t\t\t\tawait this.getRecordList(true);\r\n\t\t\t},\r\n\r\n\t\t\t// 刷新数据\r\n\t\t\tasync refreshData() {\r\n\t\t\t\tthis.pageNum = 1;\r\n\t\t\t\tthis.hasMore = true;\r\n\t\t\t\tawait this.getTotalAmount();\r\n\t\t\t\tawait this.getRecordList(true);\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t},\r\n\r\n\t\t\t// 获取已到账总额\r\n\t\t\tasync getTotalAmount() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await this.$api.mine.walletStatSimple();\r\n\t\t\t\t\tif (res.code === '200') {\r\n\t\t\t\t\t\tthis.totalAmount = res.data || 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取总额失败:', error);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 获取提现记录列表\r\n\t\t\tasync getRecordList(reset = false) {\r\n\t\t\t\tif (this.loading) return;\r\n\r\n\t\t\t\tthis.loading = true;\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst params = {\r\n\t\t\t\t\t\tstatus: this.currentStatus === '' ? '': this.currentStatus,\r\n\t\t\t\t\t\t// type: 4, // 服务费\r\n\t\t\t\t\t\tpageNum: reset ? 1 : this.pageNum,\r\n\t\t\t\t\t\tpageSize: this.pageSize\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\tconst res = await this.$api.mine.walletList(params);\r\n\r\n\t\t\t\t\tif (res.code === '200') {\r\n\t\t\t\t\t\tconst data = res.data;\r\n\t\t\t\t\t\tif (reset) {\r\n\t\t\t\t\t\t\tthis.recordList = data.list || [];\r\n\t\t\t\t\t\t\tthis.pageNum = 1;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.recordList = [...this.recordList, ...(data.list || [])];\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 判断是否还有更多数据\r\n\t\t\t\t\t\tthis.hasMore = this.pageNum < data.totalPage;\r\n\t\t\t\t\t\tif (!reset) {\r\n\t\t\t\t\t\t\tthis.pageNum++;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '获取数据失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取记录失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '网络错误，请稍后重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 加载更多\r\n\t\t\tloadMore() {\r\n\t\t\t\tif (this.hasMore && !this.loading) {\r\n\t\t\t\t\tthis.getRecordList();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 切换状态筛选\r\n\t\t\tchangeStatus(status) {\r\n\t\t\t\tif (this.currentStatus === status) return;\r\n\r\n\t\t\t\tthis.currentStatus = status;\r\n\t\t\t\tthis.pageNum = 1;\r\n\t\t\t\tthis.hasMore = true;\r\n\t\t\t\tthis.getRecordList(true);\r\n\t\t\t},\r\n\r\n\t\t\t// 处理记录点击 (Always navigate to detail)\r\n\t\t\thandleRecordTap(item) {\r\n\t\t\t\tthis.viewDetail(item.id);\r\n\t\t\t},\r\n\r\n\t\t\t// 处理状态点击 (Handle specific actions)\r\n\t\t\tasync handleStatusTap(item) {\r\n\t\t\t\tif (item.lock === 1) { // 待提现 或 已提现，未领取\r\n\t\t\t\t\tawait this.processWithdrawal(item);\r\n\t\t\t\t} else if (item.lock === 0) { // 待审核\r\n\t\t\t\t\tawait this.processCancel(item);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// If lock is not 0 or 1, clicking status also views detail\r\n\t\t\t\t\tthis.viewDetail(item.id);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 查看详情\r\n\t\t\tviewDetail(id) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/user/walletDetail?id=${id}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 处理提现请求\r\n\t\t\tasync processWithdrawal(item) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '确认提现',\r\n\t\t\t\t\tcontent: `确认提现 ￥${item.amount} 吗？`,\r\n\t\t\t\t\tconfirmText: '确认',\r\n\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\tsuccess: async (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tawait this.executeWithdrawal(item);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 处理取消提现请求 (for '待审核' status)\r\n\t\t\tasync processCancel(item) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '取消提现',\r\n\t\t\t\t\tcontent: `确认取消提现申请吗？`,\r\n\t\t\t\t\tconfirmText: '确认',\r\n\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\tsuccess: async (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tawait this.executeCancel(item);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 执行提现请求\r\n\t\t\tasync executeWithdrawal(item) {\r\n\t\t\t\t// 如果是微信提现，检查运行环境\r\n\t\t\t\tif (item.cashToType === 1) {\r\n\t\t\t\t\t// 检查是否在微信小程序环境\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\t// 在微信小程序中，可以直接处理微信提现\r\n\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t// 在APP中，提醒用户去微信小程序操作\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '微信提现需要在微信小程序中操作，请前往微信小程序进行提现',\r\n\t\t\t\t\t\tconfirmText: '知道了',\r\n\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t// #ifndef MP-WEIXIN || APP-PLUS\r\n\t\t\t\t\t// 其他环境（如H5），也提醒用户去微信小程序\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '微信提现需要在微信小程序中操作，请前往微信小程序进行提现',\r\n\t\t\t\t\t\tconfirmText: '知道了',\r\n\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.showLoading({ title: '提现中...' });\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await this.$api.mine.withdrawalRequest({ id: item.id });\r\n\r\n\t\t\t\t\tif (res.code === '200') {\r\n\t\t\t\t\t\t// 微信提现处理\r\n\t\t\t\t\t\tif (item.cashToType === 1) {\r\n\t\t\t\t\t\t\tconsole.log('uni.requestMerchantTransfer:', typeof uni.requestMerchantTransfer);\r\n\t\t\t\t\t\t\t// 检查是否支持 requestMerchantTransfer API\r\n\t\t\t\t\t\t\tif (typeof uni.requestMerchantTransfer === 'function') {\r\n\t\t\t\t\t\t\t\t// 拉起微信收款确认弹窗\r\n\t\t\t\t\t\t\t\tuni.requestMerchantTransfer({\r\n\t\t\t\t\t\t\t\t\tmchId: res.data.mchId,\r\n\t\t\t\t\t\t\t\t\tappId: res.data.appId,\r\n\t\t\t\t\t\t\t\t\tpackage: res.data.packageInfo,\r\n\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '提现成功',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.refreshData();\r\n\t\t\t\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: err.errMsg || '提现失败，请稍后重试',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\t\t\tcontent: '当前环境暂不支持微信提现功能',\r\n\t\t\t\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 支付宝或银行卡提现处理\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '提现申请已提交',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tthis.refreshData();\r\n\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '提现请求失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('提现请求失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '网络错误，请稍后重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 执行取消提现请求\r\n\t\t\tasync executeCancel(item) {\r\n\t\t\t\tuni.showLoading({ title: '取消中...' });\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst params = {\r\n\t\t\t\t\t\tid: item.id // Pass the withdrawal record ID\r\n\t\t\t\t\t};\r\n\t\t\t\t\tconst res = await this.$api.mine.cancelStatSimple(item.id); // Assuming this is the correct API call\r\n\t\t\t\t\tif (res.code === '200') {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '提现申请已取消',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.refreshData(); // Refresh the list after successful cancellation\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '取消提现失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('取消提现失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '网络错误，请稍后重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 获取提现方式文本\r\n\t\t\tgetCashToTypeText(cashToType) {\r\n\t\t\t\tconst typeMap = {\r\n\t\t\t\t\t1: '微信',\r\n\t\t\t\t\t2: '支付宝',\r\n\t\t\t\t\t3: '银行卡'\r\n\t\t\t\t};\r\n\t\t\t\treturn typeMap[cashToType] || '未知';\r\n\t\t\t},\r\n\r\n\t\t\t// 获取状态样式类\r\n\t\t\tgetStatusClass(item) {\r\n\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t'审核拒绝': 'status-rejected',\r\n\t\t\t\t\t'待提现': 'status-withdraw', // Will be overridden by lock for styling\r\n\t\t\t\t\t'已提现，未领取': 'status-withdraw', // Will be overridden by lock for styling\r\n\t\t\t\t\t'已提现': 'status-processing',\r\n\t\t\t\t\t'提现成功': 'status-success',\r\n\t\t\t\t\t'提现失败': 'status-failed',\r\n\t\t\t\t\t'已到账': 'status-success',\r\n\t\t\t\t\t'失败': 'status-failed',\r\n\t\t\t\t\t'关闭': 'status-closed',\r\n\t\t\t\t\t'待审核': 'status-pending' // Will be overridden by lock for styling\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// Apply styling based on 'lock' field\r\n\t\t\t\tif (item.lock === 1) {\r\n\t\t\t\t\treturn 'status-actionable'; // New class for actionable items (待提现, 已提现，未领取)\r\n\t\t\t\t} else if (item.lock === 0) {\r\n\t\t\t\t\treturn 'status-pending-action'; // New class for '待审核' when user can cancel\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn statusMap[item.statusText] || 'status-default';\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.page {\r\n\tbackground-color: #f0f2f5;\r\n\tmin-height: 100vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.header-stats {\r\n\tbackground: linear-gradient(135deg, #2e80fe 0%, #1e6bff 100%);\r\n\tpadding: 60rpx 30rpx 80rpx;\r\n\tborder-bottom-left-radius: 40rpx;\r\n\tborder-bottom-right-radius: 40rpx;\r\n\tbox-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);\r\n\r\n\t.stats-card {\r\n\t\ttext-align: center;\r\n\t\tcolor: #ffffff;\r\n\r\n\t\t.stats-title {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\topacity: 0.95;\r\n\t\t\tmargin-bottom: 24rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t}\r\n\r\n\t\t.stats-amount {\r\n\t\t\tfont-size: 56rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tletter-spacing: 1rpx;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.filter-section {\r\n\tbackground: #ffffff;\r\n\tmargin: -40rpx 30rpx 30rpx;\r\n\tborder-radius: 20rpx;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\r\n\tpadding: 24rpx 0;\r\n\t/* Remove position: relative and overflow: hidden if u-scroll-list handles its own clipping/gradients */\r\n\r\n\t/* If using u-scroll-list, the .filter-tabs-container and its scrollbar hiding are handled by u-scroll-list internally.\r\n\t   You might still need to style the inner items. */\r\n\r\n\t/* Remove these custom gradient overlays if using a dedicated ScrollList component */\r\n\t/*\r\n\t&::before,\r\n\t&::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tbottom: 0;\r\n\t\twidth: 60rpx;\r\n\t\tpointer-events: none;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t&::before {\r\n\t\tleft: 0;\r\n\t\tbackground: linear-gradient(to right, #ffffff 30%, rgba(255, 255, 255, 0) 100%);\r\n\t}\r\n\r\n\t&::after {\r\n\t\tright: 0;\r\n\t\tbackground: linear-gradient(to left, #ffffff 30%, rgba(255, 255, 255, 0) 100%);\r\n\t}\r\n\t*/\r\n\r\n\r\n\t/* Styling for the individual tabs within the scroll list */\r\n\t.filter-tab {\r\n\t\tpadding: 18rpx 30rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t\tborder-radius: 24rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t\ttext-align: center;\r\n\t\tflex-shrink: 0; /* Prevent tabs from shrinking */\r\n\t\tmargin: 0 10rpx; /* Add margin between tabs for better spacing, adjust as needed */\r\n\r\n\t\t&:first-child {\r\n\t\t\tmargin-left: 30rpx; /* Align first tab with filter-section padding */\r\n\t\t}\r\n\t\t&:last-child {\r\n\t\t\tmargin-right: 30rpx; /* Align last tab with filter-section padding */\r\n\t\t}\r\n\r\n\t\t&.active {\r\n\t\t\tcolor: #2e80fe;\r\n\t\t\tbackground: rgba(46, 128, 254, 0.15);\r\n\t\t\tfont-weight: 600;\r\n\t\t\tbox-shadow: 0 2rpx 8rpx rgba(46, 128, 254, 0.2);\r\n\t\t}\r\n\t}\r\n\t// The u-scroll-list component itself might have padding, so you may need to adjust the padding of .filter-section\r\n\t// or the margin of .filter-tab to ensure proper alignment and prevent double padding.\r\n}\r\n\r\n.record-list {\r\n\tpadding: 0 30rpx;\r\n\tflex-grow: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center; /* Centers each record-item horizontally */\r\n\r\n\t.record-item {\r\n\t\tbackground: #ffffff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 30rpx 40rpx;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tbox-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.07);\r\n\t\ttransition: transform 0.2s ease-in-out;\r\n\t\twidth: 100%; /* Take full width within padding */\r\n\t\tmax-width: 700rpx; /* Optional: Constrain max width for large screens */\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: translateY(2rpx);\r\n\t\t}\r\n\r\n\t\t.record-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t\t.record-amount {\r\n\t\t\t\tfont-size: 38rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t}\r\n\r\n\t\t\t.record-status {\r\n\t\t\t\tpadding: 10rpx 20rpx;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\twhite-space: nowrap;\r\n\r\n\t\t\t\t&.status-rejected {\r\n\t\t\t\t\tbackground: #fde0e0;\r\n\t\t\t\t\tcolor: #d32f2f;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.status-pending {\r\n\t\t\t\t\tbackground: #fff8e1;\r\n\t\t\t\t\tcolor: #f57c00;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.status-actionable {\r\n\t\t\t\t\tbackground: #e1f5fe;\r\n\t\t\t\t\tcolor: #039be5;\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\tbackground: #bbdefb;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.tap-hint {\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t\t\topacity: 0.85;\r\n\t\t\t\t\t\tcolor: inherit;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.status-pending-action {\r\n\t\t\t\t\tbackground: #fff8e1;\r\n\t\t\t\t\tcolor: #f57c00;\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\tbackground: #ffe0b2;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.tap-hint {\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t\t\topacity: 0.85;\r\n\t\t\t\t\t\tcolor: inherit;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.status-processing {\r\n\t\t\t\t\tbackground: #e3f2fd;\r\n\t\t\t\t\tcolor: #2196f3;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.status-success {\r\n\t\t\t\t\tbackground: #e8f5e8;\r\n\t\t\t\t\tcolor: #4caf50;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.status-failed {\r\n\t\t\t\t\tbackground: #ffebee;\r\n\t\t\t\t\tcolor: #f44336;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.status-closed {\r\n\t\t\t\t\tbackground: #f5f5f5;\r\n\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.status-default {\r\n\t\t\t\t\tbackground: #f0f0f0;\r\n\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.record-info {\r\n\t\t\t.record-time {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #888888;\r\n\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.record-method {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #666666;\r\n\t\t\t\tmargin-bottom: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.empty-state {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 150rpx 0;\r\n\tflex-grow: 1;\r\n\r\n\t.empty-image {\r\n\t\twidth: 240rpx;\r\n\t\theight: 240rpx;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\topacity: 0.7;\r\n\t}\r\n\r\n\t.empty-text {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #999999;\r\n\t\tfont-weight: 500;\r\n\t}\r\n}\r\n\r\n.load-more {\r\n\tpadding: 40rpx 0 60rpx;\r\n\ttext-align: center;\r\n\r\n\t.load-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n}\r\n</style>\r\n```", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coreWallet.vue?vue&type=style&index=0&id=f9db71dc&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coreWallet.vue?vue&type=style&index=0&id=f9db71dc&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755411055258\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}