{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/userProfile.vue?62da", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/userProfile.vue?3fef", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/userProfile.vue?d9a7", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/userProfile.vue?b555", "uni-app:///user/userProfile.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/userProfile.vue?23d2", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/userProfile.vue?e551"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "avatarUrl", "localNickName", "originalUserInfo", "nick<PERSON><PERSON>", "currentPlatform", "onLoad", "console", "methods", "set", "uni", "success", "loadUserInfo", "onChooseAvatar", "title", "icon", "chooseAvatarFromAlbum", "fail", "chooseAvatarFromFile", "onNickNameBlur", "uploadAvatarFile", "filePath", "name", "formData", "type", "response", "imageUrl", "saveUserInfo", "updatedUserInfo", "res", "setTimeout", "delta", "url", "userOut", "key", "val", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA01B,CAAgB,02BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACiD92B;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;MACA;;MACAC;MAAA;MACAC;QACAF;QACAG;MACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;IACA;EACA;EACAC,yCACA;IACAC;MACAC;QACAC;UACAJ;QACA;MACA;IACA;IACAK;MACA;MACA;MACA;MACA;MACA;MACAL;IACA;IACA;IACAM;MACAN;MACA;MACA;QACA;QACAA;MACA;QACAA;QACAG;UAAAI;UAAAC;QAAA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAL;UACA;UACAD;YAAAI;YAAAC;UAAA;QACA;QACAE;UACAP;YAAAI;YAAAC;UAAA;QACA;MACA;IACA;IAEA;IACAG;MAAA;MACA;QACAP;UACA;UACAD;YAAAI;YAAAC;UAAA;QACA;QACAE;UACAP;YAAAI;YAAAC;UAAA;QACA;MACA;IACA;IACAI;MACA;MACAZ;IACA;IACAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAV;kBAAAI;gBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAO;kBACAC;kBACAC;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAAA,KAQAA;kBAAA;kBAAA;gBAAA;gBACAC;gBACAnB;gBACAG;gBACAA;kBAAAI;kBAAAC;gBAAA;gBAAA,iCACAW;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAhB;gBACAH;gBACAG;kBACAI;kBACAC;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAjB;kBAAAI;gBAAA;gBAAA;gBAEAb,uCACA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAA;gBACA;cAAA;gBAGA;gBACA2B;kBACA3B;kBACAG;gBACA,GAEA;gBACAG;gBAAA;gBAAA,OACA;kBACAH;kBACAH;gBACA;cAAA;gBAHA4B;gBAIAtB;;gBAEA;gBACAG;gBACAH;;gBAEA;gBACA;gBACA;gBAEAG;gBACAA;kBAAAI;kBAAAC;gBAAA;;gBAEA;gBACAe;kBACApB;oBACAqB;oBACApB;sBAAA;oBAAA;oBACAM;sBACAV;sBACAG;wBACAsB;wBACArB;0BAAA;wBAAA;wBACAM;0BACAV;0BACAG;4BAAAI;4BAAAC;0BAAA;wBACA;sBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAL;gBACAH;gBACAG;kBACAI;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAkB;MACA;MACA;;MAEA;MACA;QAAAC;QAAAC;MAAA;MACA;QAAAD;QAAAC;MAAA;;MAEA;MACA;QACA/B;QACAH;MACA;MAEAS;QAAAI;QAAAC;QAAAqB;MAAA;MAEAN;QACA;QACApB;UACAsB;UACArB;YACAJ;UACA;UACAU;YACAV;YACAG;cACAqB;cACApB;gBAAA;cAAA;cACAM;gBACAV;gBACAG;kBAAAI;kBAAAC;gBAAA;cACA;YACA;UACA;QACA;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACpQA;AAAA;AAAA;AAAA;AAAykD,CAAgB,6hDAAG,EAAC,C;;;;;;;;;;;ACA7lD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/userProfile.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/userProfile.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./userProfile.vue?vue&type=template&id=3510e5a8&\"\nvar renderjs\nimport script from \"./userProfile.vue?vue&type=script&lang=js&\"\nexport * from \"./userProfile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./userProfile.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/userProfile.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=template&id=3510e5a8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"user-info\">\n      <view class=\"avatar-wrapper\">\n        <!-- 小程序环境使用微信头像选择 -->\n        <!-- #ifdef MP-WEIXIN -->\n        <button class=\"choose-avatar-button\" open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\">\n          <image class=\"avatar\" :src=\"userInfo.avatarUrl || '/static/mine/default_user.png'\" mode=\"aspectFill\" />\n        </button>\n        <!-- #endif -->\n\n        <!-- APP环境使用相册选择 -->\n        <!-- #ifdef APP-PLUS -->\n        <view class=\"choose-avatar-button\" @click=\"chooseAvatarFromAlbum\">\n          <image class=\"avatar\" :src=\"userInfo.avatarUrl || '/static/mine/default_user.png'\" mode=\"aspectFill\" />\n          <view class=\"avatar-overlay\">\n            <text class=\"change-text\">点击更换</text>\n          </view>\n        </view>\n        <!-- #endif -->\n\n        <!-- H5环境使用文件选择 -->\n        <!-- #ifdef H5 -->\n        <view class=\"choose-avatar-button\" @click=\"chooseAvatarFromFile\">\n          <image class=\"avatar\" :src=\"userInfo.avatarUrl || '/static/mine/default_user.png'\" mode=\"aspectFill\" />\n          <view class=\"avatar-overlay\">\n            <text class=\"change-text\">点击更换</text>\n          </view>\n        </view>\n        <!-- #endif -->\n      </view>\n      <!-- <view class=\"nickname-section\">\n        <text class=\"label\">昵称：</text>\n        <input\n          class=\"nickname-input\"\n          type=\"nickname\"\n          placeholder=\"请输入昵称\"\n          v-model=\"localNickName\"\n          @blur=\"onNickNameBlur\"\n        />\n      </view> -->\n      <button class=\"save-button\" @click=\"saveUserInfo\">保存</button>\n      <button class=\"save-button\" @click=\"set\">系统设置</button>\n      <button class=\"save-button\" @click=\"userOut\">退出</button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapMutations } from 'vuex';\nimport { getCurrentPlatform, chooseAvatar, clearUserStorage } from '../utils/platform-utils.js';\n\nexport default {\n  data() {\n    return {\n      userInfo: {\n        avatarUrl: '', // Store the temporary or uploaded avatar URL\n      },\n      localNickName: '微信用户', // Store the temporary nickname\n      originalUserInfo: {\n        avatarUrl: '',\n        nickName: '',\n      },\n      currentPlatform: '', // 当前平台类型\n    };\n  },\n  onLoad() {\n    this.currentPlatform = getCurrentPlatform();\n    console.log('当前平台:', this.currentPlatform);\n    this.loadUserInfo();\n  },\n  methods: {\n    ...mapMutations('user', ['updateUserItem']),\n    set() {\n      uni.openSetting({\n        success(res) {\n          console.log(res);\n        },\n      });\n    },\n    loadUserInfo() {\n      const cachedUserInfo = uni.getStorageSync('userInfo') || {};\n      this.userInfo.avatarUrl = cachedUserInfo.avatarUrl || '';\n      this.localNickName = cachedUserInfo.nickName || '微信用户';\n      this.originalUserInfo.avatarUrl = this.userInfo.avatarUrl;\n      this.originalUserInfo.nickName = this.localNickName;\n      console.log('Loaded user info:', cachedUserInfo);\n    },\n    // 微信小程序头像选择\n    onChooseAvatar(e) {\n      console.log('onChooseAvatar event:', e);\n      const { avatarUrl } = e.detail;\n      if (avatarUrl) {\n        this.userInfo.avatarUrl = avatarUrl;\n        console.log('Selected avatar:', avatarUrl);\n      } else {\n        console.error('Failed to get avatarUrl from event detail.');\n        uni.showToast({ title: '选择头像失败', icon: 'error' });\n      }\n    },\n\n    // APP环境从相册选择头像\n    chooseAvatarFromAlbum() {\n      chooseAvatar({\n        success: (result) => {\n          this.userInfo.avatarUrl = result.avatarUrl;\n          uni.showToast({ title: '头像选择成功', icon: 'success' });\n        },\n        fail: (err) => {\n          uni.showToast({ title: '选择头像失败', icon: 'error' });\n        }\n      });\n    },\n\n    // H5环境文件选择\n    chooseAvatarFromFile() {\n      chooseAvatar({\n        success: (result) => {\n          this.userInfo.avatarUrl = result.avatarUrl;\n          uni.showToast({ title: '头像选择成功', icon: 'success' });\n        },\n        fail: (err) => {\n          uni.showToast({ title: '选择头像失败', icon: 'error' });\n        }\n      });\n    },\n    onNickNameBlur(e) {\n      this.localNickName = e.detail.value;\n      console.log('Nickname input:', this.localNickName);\n    },\n    async uploadAvatarFile(tempFilePath) {\n      uni.showLoading({ title: '上传中' });\n      try {\n        const response = await this.$api.base.uploadFile({\n          filePath: tempFilePath,\n          name: 'multipartFile',\n          formData: {\n            type: 'picture',\n          },\n        });\n\n        if (response) {\n          const imageUrl = response; // Assume response is the direct image URL, consistent with second code\n          console.log('Avatar uploaded successfully:', imageUrl);\n          uni.hideLoading();\n          uni.showToast({ title: '上传成功', icon: 'success' });\n          return imageUrl;\n        } else {\n          throw new Error('上传失败');\n        }\n      } catch (error) {\n        uni.hideLoading();\n        console.error('Upload failed:', error);\n        uni.showToast({\n          title: error.message || '上传失败，请重试',\n          icon: 'none',\n        });\n        throw error;\n      }\n    },\n    async saveUserInfo() {\n      uni.showLoading({ title: '保存中...' });\n      try {\n        let avatarUrl = this.userInfo.avatarUrl;\n        // If avatar has changed, upload it first\n        if (avatarUrl && avatarUrl !== this.originalUserInfo.avatarUrl) {\n          avatarUrl = await this.uploadAvatarFile(avatarUrl);\n          this.userInfo.avatarUrl = avatarUrl; // Update with uploaded URL\n        }\n\n        // Prepare updated user info\n        const updatedUserInfo = {\n          avatarUrl: avatarUrl || '',\n          nickName: this.localNickName,\n        };\n\n        // Update backend\n        console.log('Updating user info with:', updatedUserInfo);\n        const res = await this.$api.user.updataInfo({\n          nickName: updatedUserInfo.nickName,\n          avatarUrl: updatedUserInfo.avatarUrl,\n        });\n        console.log('Update info response:', res);\n\n        // Save to local storage\n        uni.setStorageSync('userInfo', updatedUserInfo);\n        console.log('Saved to local storage:', updatedUserInfo);\n\n        // Update original info\n        this.originalUserInfo.avatarUrl = updatedUserInfo.avatarUrl;\n        this.originalUserInfo.nickName = updatedUserInfo.nickName;\n\n        uni.hideLoading();\n        uni.showToast({ title: '保存成功', icon: 'success' });\n\n        // Navigate back or redirect\n        setTimeout(() => {\n          uni.navigateBack({\n            delta: 1,\n            success: () => console.log('Navigation back successful'),\n            fail: (err) => {\n              console.error('Navigation back failed:', err);\n              uni.redirectTo({\n                url: '/pages/mine/mine',\n                success: () => console.log('Redirected to mine page'),\n                fail: (redirectErr) => {\n                  console.error('Redirect failed:', redirectErr);\n                  uni.showToast({ title: '返回失败，请手动返回', icon: 'error' });\n                },\n              });\n            },\n          });\n        }, 1000);\n      } catch (err) {\n        uni.hideLoading();\n        console.error('Failed to save user info:', err);\n        uni.showToast({\n          title: '保存失败: ' + (err.message || '未知错误'),\n          icon: 'error',\n        });\n      }\n    },\n    userOut() {\n      // 使用工具函数清除用户存储\n      clearUserStorage();\n\n      // 清除 Vuex 中的用户数据\n      this.updateUserItem({ key: 'autograph', val: '' });\n      this.updateUserItem({ key: 'userInfo', val: {} });\n\n      // 重置页面数据\n      this.userInfo = {\n        nickName: '',\n        avatarUrl: '/static/mine/default_user.png'\n      };\n\n      uni.showToast({ title: '已退出登录', icon: 'success', duration: 2000 });\n\n      setTimeout(() => {\n        // 使用 reLaunch 确保页面完全重新加载，清除所有状态\n        uni.reLaunch({\n          url: '/pages/mine',\n          success: () => {\n            console.log('退出登录成功，已跳转到 mine 页面');\n          },\n          fail: (err) => {\n            console.error('跳转失败，尝试 navigateBack:', err);\n            uni.navigateBack({\n              delta: 1,\n              success: () => console.log('Navigation back successful'),\n              fail: (backErr) => {\n                console.error('Navigation back failed:', backErr);\n                uni.showToast({ title: '返回失败，请手动返回', icon: 'error' });\n              },\n            });\n          },\n        });\n      }, 1000);\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\">\n.container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  padding-top: 40rpx;\n\n  .user-info {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    background-color: #fff;\n    width: 100%;\n    padding: 40rpx;\n    border-radius: 20rpx;\n    margin-top: 40rpx;\n\n    .avatar-wrapper {\n      position: relative;\n      width: 160rpx;\n      height: 160rpx;\n      margin-bottom: 40rpx;\n\n      .choose-avatar-button {\n        width: 100%;\n        height: 100%;\n        display: block;\n        margin: 0;\n        border-radius: 50%;\n        border: none;\n        background-color: transparent;\n        overflow: hidden;\n        line-height: normal;\n        position: relative;\n\n        &::after {\n          border: none;\n        }\n\n        .avatar {\n          width: 100%;\n          height: 100%;\n          display: block;\n          border: 1px solid #ddd;\n          box-sizing: border-box;\n          border-radius: 50%;\n        }\n\n        // APP和H5环境的头像覆盖层\n        .avatar-overlay {\n          position: absolute;\n          bottom: 0;\n          left: 0;\n          right: 0;\n          background: rgba(0, 0, 0, 0.6);\n          color: white;\n          text-align: center;\n          padding: 8rpx 0;\n          border-radius: 0 0 50% 50%;\n\n          .change-text {\n            font-size: 20rpx;\n            color: white;\n          }\n        }\n      }\n    }\n\n    .nickname-section {\n      display: flex;\n      align-items: center;\n      width: 100%;\n      margin-bottom: 40rpx;\n\n      .label {\n        font-size: 32rpx;\n        color: #333;\n        margin-right: 20rpx;\n        white-space: nowrap;\n      }\n\n      .nickname-input {\n        flex: 1;\n        height: 80rpx;\n        background-color: #f5f5f5;\n        border-radius: 10rpx;\n        padding: 0 20rpx;\n        font-size: 28rpx;\n        color: #333;\n        border: 1px solid #ddd;\n        box-sizing: border-box;\n        min-width: 0;\n      }\n    }\n\n    .save-button {\n      width: 100%;\n      height: 90rpx;\n      background-color: #599eff;\n      border-radius: 45rpx;\n      color: #fff;\n      font-size: 32rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-top: 20rpx;\n    }\n  }\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755411060762\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}