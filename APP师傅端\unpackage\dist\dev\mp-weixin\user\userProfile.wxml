<view class="container"><view class="user-info"><view class="avatar-wrapper"><button class="choose-avatar-button" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e"><image class="avatar" src="{{userInfo.avatarUrl||'/static/mine/default_user.png'}}" mode="aspectFill"></image></button></view><button data-event-opts="{{[['tap',[['saveUserInfo',['$event']]]]]}}" class="save-button" bindtap="__e">保存</button><button data-event-opts="{{[['tap',[['set',['$event']]]]]}}" class="save-button" bindtap="__e">系统设置</button><button data-event-opts="{{[['tap',[['userOut',['$event']]]]]}}" class="save-button" bindtap="__e">退出</button></view></view>