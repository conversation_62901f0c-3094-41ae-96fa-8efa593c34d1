@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-top: 40rpx;
}
.container .user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  width: 100%;
  padding: 40rpx;
  border-radius: 20rpx;
  margin-top: 40rpx;
}
.container .user-info .avatar-wrapper {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
}
.container .user-info .avatar-wrapper .choose-avatar-button {
  width: 100%;
  height: 100%;
  display: block;
  margin: 0;
  border-radius: 50%;
  border: none;
  background-color: transparent;
  overflow: hidden;
  line-height: normal;
  position: relative;
}
.container .user-info .avatar-wrapper .choose-avatar-button::after {
  border: none;
}
.container .user-info .avatar-wrapper .choose-avatar-button .avatar {
  width: 100%;
  height: 100%;
  display: block;
  border: 1px solid #ddd;
  box-sizing: border-box;
  border-radius: 50%;
}
.container .user-info .avatar-wrapper .choose-avatar-button .avatar-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  text-align: center;
  padding: 8rpx 0;
  border-radius: 0 0 50% 50%;
}
.container .user-info .avatar-wrapper .choose-avatar-button .avatar-overlay .change-text {
  font-size: 20rpx;
  color: white;
}
.container .user-info .nickname-section {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 40rpx;
}
.container .user-info .nickname-section .label {
  font-size: 32rpx;
  color: #333;
  margin-right: 20rpx;
  white-space: nowrap;
}
.container .user-info .nickname-section .nickname-input {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  border: 1px solid #ddd;
  box-sizing: border-box;
  min-width: 0;
}
.container .user-info .save-button {
  width: 100%;
  height: 90rpx;
  background-color: #599eff;
  border-radius: 45rpx;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
}

